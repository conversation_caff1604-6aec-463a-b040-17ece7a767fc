"""
Query Schema生成工具

将用户自然语言查询转换为符合FLIGHT_QUERY_SCHEMA格式的标准查询。
核心功能：用户提问 + Schema规范 + 模型信息 + 示例数据 + 提示词 → LLM → 标准查询
"""

import json
from typing import Dict, Any, List
from langchain_core.tools import tool

from .flight_query_schema import (
    QUERY_OPERATORS,
    AGGREGATE_FUNCTIONS,
    ANNOTATION_FUNCTIONS,
    FLIGHT_QUERY_SCHEMA,
)
from .model_context_tool import get_models_context, CustomJSONEncoder


@tool
def generate_flight_query(user_query: str, model_names: str = "aviation_flight,aviation_airports,aviation_aircraft") -> str:
    """
    将用户自然语言查询转换为符合FLIGHT_QUERY_SCHEMA格式的标准查询
    
    此工具已集成模型上下文信息，一次性提供完整的查询生成环境，包括：
    - Schema规范和操作符定义
    - 实际的模型结构信息和字段定义
    - 样例数据用于理解数据特征
    - Few-shot示例用于指导查询生成
    
    Args:
        user_query: 用户的自然语言查询
        model_names: 逗号分隔的模型名称，默认包含所有航班相关模型
        
    Returns:
        str: 包含完整上下文信息的JSON，供LLM生成查询使用
    """
    try:
        # 获取实际的模型上下文信息（结构+样例数据）
        model_list = [name.strip() for name in model_names.split(',') if name.strip()]
        models_context = get_models_context(
            model_list,
            sample_limit=3,  # 减少样例数量以节省token
            sample_random=True,
            include_relationships=True,
            include_meta=True,
            exclude_sys_fields=True
        )
        
        # 构建完整的上下文信息
        context = {
            "user_query": user_query,
            "task": "请根据用户查询和提供的模型信息，生成符合FLIGHT_QUERY_SCHEMA格式的标准查询JSON",
            
            # Schema规范
            "flight_query_schema": FLIGHT_QUERY_SCHEMA,
            
            # 操作符信息
            "query_operators": QUERY_OPERATORS,
            
            # 聚合和注解函数
            "aggregate_functions": AGGREGATE_FUNCTIONS,
            "annotation_functions": ANNOTATION_FUNCTIONS,
            
            # 实际模型信息和样例数据
            "models_context": models_context,
            
            # Few-shot示例 - 提高LLM生成准确率
            "few_shot_examples": get_few_shot_examples(),
            
            # 生成指导
            "instructions": [
                "1. 仔细分析用户查询，参考few_shot_examples中的模式",
                "2. 从models_context中查看可用的模型和字段信息",
                "3. 根据models_context中的sample_data理解实际数据特征",
                "4. 确定要查询的数据模型（aviation_flight/aviation_airports/aviation_aircraft）",
                "5. 基于models_context中的实际字段信息识别查询条件",
                "6. 选择合适的字段和操作符（确保字段在models_context中真实存在）",
                "7. 如果涉及统计、聚合，添加aggregations配置",
                "8. 如果涉及排名、分类，添加annotations配置",
                "9. 设置合理的排序和分页参数",
                "10. 确保生成的JSON严格符合FLIGHT_QUERY_SCHEMA格式",
                "11. 只返回最终的查询JSON，不要包含解释文字",
                "12. 参考few_shot_examples的简洁风格，避免过度复杂的查询结构",
                "13. 对于跨表查询，使用Django ORM的双下划线语法，如 aircraft__military"
            ],
            
            # 关键提示
            "important_notes": [
                "✅ 所有模型信息和样例数据已经包含在models_context中",
                "🎯 请直接使用models_context中的字段信息，无需调用其他工具",
                "🔍 确保使用的字段在models_context的fields中存在",
                "📊 参考sample_data了解数据的实际格式和内容",
                "⚡ 生成的查询将由 comprehensive_flight_query 工具进行验证和执行"
            ]
        }
        
        return json.dumps(context, ensure_ascii=False, indent=2, cls=CustomJSONEncoder)
        
    except Exception as e:
        return json.dumps({
            "error": f"生成查询上下文失败: {str(e)}",
            "user_query": user_query
        }, ensure_ascii=False, indent=2)


def get_few_shot_examples() -> List[Dict[str, Any]]:
    """
    获取针对性的few-shot示例，涵盖常见查询模式
    
    Returns:
        List[Dict]: 包含用户查询和对应生成查询的示例列表
    """
    # 提供基础的静态示例，避免依赖动态字段获取
    return [
        {
            "user_query": "查询东方航空的航班",
            "generated_query": {
                "model": "aviation_flight",
                "conditions": {
                    "AND": [
                        {"field": "airline_name", "operator": "icontains", "value": "China Eastern"}
                    ]
                },
                "ordering": ["-departure_scheduled_time"],
                "limit": 10
            }
        },
        {
            "user_query": "查询今天的航班",
            "generated_query": {
                "model": "aviation_flight",
                "conditions": {
                    "AND": [
                        {"field": "from_airport__prefecture", "operator": "icontains", "value": "北京"},
                        {"field": "departure_scheduled_time", "operator": "date", "value": "2025-08-21"}
                    ]
                },
                "fields": ["flight_number_iata", "airline_name", "from_airport__name", "to_airport__name", "departure_scheduled_time"],
                "ordering": ["departure_scheduled_time"],
                "limit": 20
            }
        },
        {
            "user_query": "统计各航空公司的航班数量",
            "generated_query": {
                "model": "aviation_flight",
                "conditions": {},
                "aggregations": {
                    "group_by": ["airline_name"],
                    "functions": {
                        "flight_count": {
                            "function": "count"
                        }
                    }
                },
                "ordering": ["-flight_count"],
                "limit": 15
            }
        },
        {
            "user_query": "查询中国的国际机场",
            "generated_query": {
                "model": "aviation_airports",
                "conditions": {
                    "AND": [
                        {"field": "country", "operator": "exact", "value": "China"},
                        {"field": "name_en", "operator": "icontains", "value": "international"}
                    ]
                },
                "fields": ["icao_code", "iata_code", "name", "province", "prefecture"],
                "ordering": ["name"],
                "limit": 10
            }
        },
        {
            "user_query": "查询波音737系列飞机",
            "generated_query": {
                "model": "aviation_aircraft",
                "conditions": {
                    "AND": [
                        {"field": "model_name", "operator": "contains", "value": "737"},
                        {"field": "manufacturer", "operator": "icontains", "value": "Boeing"}
                    ]
                },
                "fields": ["registration", "model_name", "manufacturer", "airline_name", "total_passenger"],
                "ordering": ["-total_passenger"],
                "limit": 10
            }
        },
        {
            "user_query": "查询延误的航班",
            "generated_query": {
                "model": "aviation_flight",
                "fields": ["flight_number_iata", "airline_name", "from_airport__name", "to_airport__name", "departure_scheduled_time"],
                "conditions": {
                    "AND": [
                        {"field": "departure_actual_time", "operator": "isnull", "value": False},
                        {"field": "departure_scheduled_time", "operator": "isnull", "value": False}
                    ]
                },
                "annotations": {
                    "delay_minutes": {
                        "function": "time_diff",
                        "field1": "departure_actual_time",
                        "field2": "departure_scheduled_time",
                        "unit": "minutes"
                    }
                },
                "conditions_after_annotations": {
                    "AND": [
                        {"field": "delay_minutes", "operator": "gt", "value": 15}
                    ]
                },
                "ordering": ["-delay_minutes"],
                "limit": 10
            }
        },
        {
            "user_query": "统计各机场的出发航班数",
            "generated_query": {
                "model": "aviation_flight",
                "conditions": {},
                "aggregations": {
                    "group_by": ["from_airport__name"],
                    "functions": {
                        "flight_count": {
                            "function": "count"
                        }
                    }
                },
                "ordering": ["-flight_count"],
                "limit": 10
            }
        },
        {
            "user_query": "查询货机",
            "generated_query": {
                "model": "aviation_aircraft",
                "conditions": {
                    "AND": [
                        {"field": "as_cargo", "operator": "exact", "value": True}
                    ]
                },
                "fields": ["registration", "model_name", "manufacturer", "airline_name"],
                "ordering": ["airline_name"],
                "limit": 10
            }
        }
    ]

